<?php

namespace Tests\Feature;

use Tests\TestCase;

class ExampleTest extends TestCase
{
    /**
     * Test that the application returns a successful response.
     */
    public function test_the_application_returns_a_successful_response(): void
    {
        // Test a route that doesn't require authentication
        $response = $this->get('/');

        // Accept 200 (success), 302 (redirect), or 404 (route not found)
        $this->assertContains($response->getStatusCode(), [200, 302, 404]);
    }

    /**
     * Test that database isolation is working correctly.
     * This test verifies that we're using SQLite in-memory database.
     */
    public function test_database_isolation_is_working(): void
    {
        // This will throw an exception if isolation is not working
        $this->assertDatabaseIsolation();

        // Additional verification that we can interact with database safely
        $this->assertDatabaseEmpty('users');

        // Test that we can create data without affecting development database
        \App\Models\User::factory()->create([
            'email' => '<EMAIL>'
        ]);

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>'
        ]);

        // After this test completes, the RefreshDatabase trait will clean up
        // and the data won't persist to the next test
    }
}
