<!DOCTYPE html>
<html lang="en" dir="">

<head>
    <!-- Required Meta Tags Always Come First -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="title" content="Sign in to GravityWrite; Your AI Writing Assistant">
    <meta name="description"
        content="Log in to GravityWrite account and unlock a world of seamless writing experience. Access your drafts, projects, and templates effortlessly. Start writing smarter with GravityWrite today!">
    <!-- Title -->
    <title>GravityWrite | Unleash Your Writing Potential</title>

    <!-- Favicon -->
    <link rel="shortcut icon" href="{{ asset('assets/img/favicon.svg') }}">

    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">

    <!-- CSS Implementing Plugins -->
    <link rel="stylesheet" href="{{ asset('vendor/bootstrap-icons/font/bootstrap-icons.css') }}">
    <link rel="stylesheet" href="{{ asset('vendor/hs-mega-menu/dist/hs-mega-menu.min.css') }}">
    <link rel="stylesheet" href="{{ asset('vendor/aos/dist/aos.css') }}">
    <link rel="stylesheet" href="{{ asset('vendor/swiper/swiper-bundle.min.css') }}">

    <!-- CSS Front Template -->
    <link rel="stylesheet" href="{{ asset('assets/css/theme.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/snippets.min.css') }}">

    <!-- JQuery -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>

    <!-- CSS Front Doc -->
    <style type="text/css">
        .form-label {
            font-size: 1rem !important;
        }

        #content {
            min-height: 100vh;
        }
    </style>
</head>

<body>
    <!-- ========== MAIN CONTENT ========== -->
    <main id="content" role="main" class="align-content-center">

        <!-- Form -->
        <div class="container content-space-3 content-space-t-lg-4 content-space-b-lg-3">
            <div class="flex-grow-1 mx-auto" style="max-width: 28rem;">
                <!-- Heading -->
                <div class="text-center mb-5 mb-md-7">
                    <img src="{{ asset('assets/img/logo-dual-dark.png') }}" width="45%" alt="">
                    <h1 class="h2 mt-6">Welcome back</h1>
                    <p>Login to manage your account.</p>
                </div>
                <!-- End Heading -->

                {{-- List all errors --}}
                @error('is_already_logged_in')
                <x-model id="alreadyLoggedInModal" :form="[route('login.force'), 'post']">
                    <!-- Slot Model Body -->
                    <x-slot name="body">
                        <h4 class="modal-title">Your previous session is still active</h4>
                        <p class="pt-3">Do you want to log out from it and continue here?</p>
                    </x-slot>

                    <!-- Slot Model Footer -->
                    <x-slot name="footer">
                        <input type="text" hidden name="email" value="{{ session('email') }}">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Continue here</button>
                    </x-slot>
                </x-model>

                <script>
                    $(document).ready(function() {
                        $('#alreadyLoggedInModal').modal('show');
                        $('#alreadyLoggedInModal button[data-dismiss="modal"]').click(function() {
                            $('#alreadyLoggedInModal').modal('hide');
                        });
                    });
                </script>
                @enderror

                <!-- Form -->
                <form action="{{ route('login') }}" method="POST" class="js-validate needs-validation"
                    style="margin-top: -16px;" novalidate>
                    @csrf
                    <!-- Form -->
                    <div class="text-center mb-4 bottom-img google_button" style="margin-left: -13px;">
                        <a href="{{ route('passport.socialite.redirect', 'google') }}">
                            <img src="{{ asset('assets/img/auth/google_sign_in.svg') }}" alt="Gravity Write"
                                style="display: flex;width: 471px;margin-bottom: -7px;">
                        </a>
                        <img class="mt-4 mb-2" src="{{ asset('assets/img/auth/dash_separator.svg') }}"
                            alt="Gravity Write">
                    </div><!-- <br> -->
                    <div class="mb-4">
                        <label class="form-label" for="email-input">Email Address</label>
                        <input type="email" class="form-control form-control-lg" name="email" value="{{ old('email', $email ?? '') }}"
                            id="email-input" placeholder="<EMAIL>" aria-label="Enter your email address"
                            required autofocus>

                        <span class="invalid-feedback" style="{{ $errors->has('email') ? 'display:none;' : '' }}">Please
                            enter valid email address.</span>
                    </div>
                    <!-- End Form -->

                    <!-- Form -->
                    <div class="password_hide">
                        <div class="d-flex justify-content-between align-items-center">
                            <label class="form-label" for="password-input">Login Password</label>

                            <a class="form-label-link" href="{{ route('password.request') }}">Forgot password?</a>
                        </div>

                        <div class="input-group input-group-merge" data-hs-validation-validate-class>
                            <input type="password"
                                class="js-toggle-password form-control form-control-lg @error('password') is-invalid @enderror"
                                name="password" id="password-input"
                                placeholder="Create a strong password (min 8 characters)"
                                aria-label="Create a strong password" required minlength="8"
                                data-hs-toggle-password-options='{
                                    "target": "#changePassTarget",
                                    "defaultClass": "bi-eye-slash",
                                    "showClass": "bi-eye",
                                    "classChangeTarget": "#changePassIcon"
                                }'>
                            <a id="changePassTarget" class="input-group-append input-group-text" href="javascript:;">
                                <i id="changePassIcon" class="bi-eye"></i>
                            </a>
                        </div>
                        <span class="invalid-feedback">Please enter a valid password.</span>
                    </div>

                    <div class="mb-4 password_hide">
                        @error('email')
                        <span class="bk-error" style="color: red"><small>{{ $message }}</small></span>
                        @enderror
                    </div>
                    <!-- End Form -->

                    <div class="d-grid mb-3">
                        <button type="submit" class="btn btn-primary btn-lg">Login</button>
                    </div>
                    <div class="text-center">
                        <p>Don't have an account yet? <a class="link" href="{{ route('register') }}">Create New
                                Account</a></p>
                    </div>
                </form>

                <!-- End Form -->
            </div>
        </div>
        <!-- End Form -->
    </main>
    <!-- ========== END MAIN CONTENT ========== -->

    <!-- ========== SECONDARY CONTENTS ========== -->
    <!-- Go To -->
    <a class="js-go-to go-to position-fixed" href="javascript:;" style="visibility: hidden;" data-hs-go-to-options='{
       "offsetTop": 700,
       "position": {
         "init": {
           "right": "2rem"
         },
         "show": {
           "bottom": "2rem"
         },
         "hide": {
           "bottom": "-2rem"
         }
       }
     }'>
        <i class="bi-chevron-up"></i>
    </a>
    <!-- ========== END SECONDARY CONTENTS ========== -->

    <!-- JS Global Compulsory  -->
    <script src="{{ asset('vendor/bootstrap/dist/js/bootstrap.bundle.min.js') }}"></script>

    <!-- JS Implementing Plugins -->
    <script src="{{ asset('vendor/hs-header/dist/hs-header.min.js') }}"></script>
    <script src="{{ asset('vendor/hs-mega-menu/dist/hs-mega-menu.min.js') }}"></script>
    <script src="{{ asset('vendor/hs-show-animation/dist/hs-show-animation.min.js') }}"></script>
    <script src="{{ asset('vendor/hs-go-to/dist/hs-go-to.min.js') }}"></script>
    <script src="{{ asset('vendor/aos/dist/aos.js') }}"></script>
    <script src="{{ asset('vendor/swiper/swiper-bundle.min.js') }}"></script>
    <script src="{{ asset('vendor/hs-nav-scroller/dist/hs-nav-scroller.min.js') }}"></script>
    <script src="{{ asset('vendor/hs-toggle-switch/dist/hs-toggle-switch.min.js') }}"></script>
    <script src="{{ asset('vendor/hs-toggle-password/dist/hs-toggle-password.js') }}"></script>

    <!-- JS Front -->
    <script src="{{ asset('assets/js/theme.min.js') }}"></script>

    <!-- JS Plugins Init. -->
    <script>
        (function() {

            // INITIALIZATION OF MEGA MENU
            // =======================================================
            new HSMegaMenu('.js-mega-menu', {
                desktop: {
                    position: 'left'
                }
            })


            // INITIALIZATION OF SHOW ANIMATIONS
            // =======================================================
            new HSShowAnimation('.js-animation-link')


            // INITIALIZATION OF BOOTSTRAP VALIDATION
            // =======================================================
            HSBsValidation.init('.js-validate')


            // INITIALIZATION OF GO TO
            // =======================================================
            new HSGoTo('.js-go-to')


            // INITIALIZATION OF TOGGLE PASSWORD
            // =======================================================
            new HSTogglePassword('.js-toggle-password')

            // INITIALIZATION OF AOS
            // =======================================================
            AOS.init({
                duration: 650,
                once: true
            });

            // INITIALIZATION OF TOGGLE SWITCH
            // =======================================================
            new HSToggleSwitch('.js-toggle-switch')

            // INITIALIZATION OF SWIPER
            // =======================================================
            var swiper = new Swiper('.js-swiper-hero-clients', {
                slidesPerView: 2,
                breakpoints: {
                    380: {
                        slidesPerView: 3,
                        spaceBetween: 15,
                    },
                    768: {
                        slidesPerView: 4,
                        spaceBetween: 15,
                    },
                    1024: {
                        slidesPerView: 5,
                        spaceBetween: 15,
                    },
                },
            });


            // INITIALIZATION OF NAV SCROLLER
            // =======================================================
            new HsNavScroller('.js-nav-scroller')

            // INITIALIZATION OF BOOTSTRAP DROPDOWN
            // =======================================================
            HSBsDropdown.init()
        })()
    </script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <script src="{{ asset('assets/js/focus-input.js') }}"></script>
    <script type="text/javascript">
        $(document).ready(function() {
            var title = 'Login to GravityWrite';
            document.title = title;
            focusOnInput();
        });

        document.querySelector('form').addEventListener('submit', function() {
            this.querySelector('button[type="submit"]').disabled = true;
        });

        document.querySelectorAll('form input').forEach(input => {
            input.addEventListener('keyup', function() {
                this.closest('form').querySelector('button[type="submit"]').disabled = false;
                this.closest('form').querySelector('.invalid-feedback').removeAttribute('style');
                this.closest('form').querySelector('.bk-error').style.display = 'none';
            });
        });
    </script>
</body>

</html>
