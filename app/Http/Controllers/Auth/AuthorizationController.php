<?php

namespace App\Http\Controllers\Auth;

use App\Traits\Passport\PassportSessionTrait;
use App\Traits\UserLogoutTrait;
use Illuminate\Contracts\Auth\StatefulGuard;
use Illuminate\Http\Request;
use Lara<PERSON>\Passport\Bridge\User;
use <PERSON><PERSON>\Passport\ClientRepository;
use Lara<PERSON>\Passport\Contracts\AuthorizationViewResponse;
use <PERSON><PERSON>\Passport\Http\Controllers\AuthorizationController as PassportAuthorizationController;
use <PERSON><PERSON>\Passport\TokenRepository;
use League\OAuth2\Server\AuthorizationServer;
use Nyholm\Psr7\Response as Psr7Response;
use Psr\Http\Message\ServerRequestInterface;

class AuthorizationController extends PassportAuthorizationController
{
    use PassportSessionTrait;
    use UserLogoutTrait;

    /**
     * Create a new controller instance.
     *
     * @param  \League\OAuth2\Server\AuthorizationServer  $server
     * @param  \Illuminate\Contracts\Auth\StatefulGuard  $guard
     * @param  \Laravel\Passport\Contracts\AuthorizationViewResponse  $response
     * @return void
     */
    public function __construct(
        AuthorizationServer $server,
        StatefulGuard $guard,
        AuthorizationViewResponse $response,
    ) {
        $this->server = $server;
        $this->guard = $guard;
        $this->response = $response;
    }


    /**
     * Authorize a client to access the user's account.
     *
     * @param  \Psr\Http\Message\ServerRequestInterface  $psrRequest
     * @param  \Illuminate\Http\Request  $request
     * @param  \Laravel\Passport\ClientRepository  $clients
     * @param  \Laravel\Passport\TokenRepository  $tokens
     * @return \Illuminate\Http\Response|\Laravel\Passport\Contracts\AuthorizationViewResponse
     */
    #[\Override]
    public function authorize(
        ServerRequestInterface $psrRequest,
        Request $request,
        ClientRepository $clients,
        TokenRepository $tokens,
    ) {
        // Check if this is a registration flow and logout existing user if needed
        $this->handleRegistrationLogout($request);

        // Instead of flush(), which destroys the session completely,
        // use forget() to clear specific keys or regenerate() to create a new session ID
        session()->regenerate(true); // true means delete the old session
        session()->put('is_first_time', true);

        return parent::authorize($psrRequest, $request, $clients, $tokens);
    }

    /**
     * Handle automatic logout for registration flows.
     *
     * If the OAuth authorization request contains 'is_register=1' parameter
     * and a user is currently authenticated, log out that user.
     * This method ensures logout only happens once per OAuth request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return bool True if logout was performed, false otherwise
     */
    protected function handleRegistrationLogout(Request $request): bool
    {
        // Only proceed if this is a registration request
        if (!$request->has('is_register') || !$request->get('is_register') || !$this->guard->check()) {
            return false;
        }

        // Create a unique identifier for this OAuth request to prevent duplicate logouts
        // $oauthRequestId = $this->generateOAuthRequestId($request);
        // $logoutSessionKey = "oauth_logout_performed_{$oauthRequestId}";

        // // Check if we've already performed logout for this specific OAuth request or user is not authenticated
        // if (session()->has($logoutSessionKey) || !$this->guard->check()) {
        //     return false;
        // }

        // // Mark that we're about to perform logout for this OAuth request
        // session()->put($logoutSessionKey, true);

        // Log out the current user
        $userId = $this->revokeAllTokens(userId: auth()->id());

        $this->revokeAllSessions($userId ?? auth()->id);

        return true;
    }

    /**
     * Generate a unique identifier for the OAuth request to track logout state.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return string
     */
    protected function generateOAuthRequestId(Request $request): string
    {
        // Use client_id, redirect_uri, and state to create a unique identifier
        $clientId = $request->get('client_id', '');
        $redirectUri = $request->get('redirect_uri', '');

        // Create a hash of the key parameters to ensure uniqueness
        return md5($clientId . '|' . $redirectUri);
    }

    /**
     * Approve the authorization request.
     *
     * @param \League\OAuth2\Server\RequestTypes\AuthorizationRequest $authRequest
     * @param \Illuminate\Contracts\Auth\Authenticatable              $user
     *
     * @return \Illuminate\Http\Response
     */
    #[\Override]
    protected function approveRequest($authRequest, $user)
    {
        $authRequest->setUser(new User($user->getAuthIdentifier()));

        $authRequest->setAuthorizationApproved(true);

        return $this->withErrorHandling(function () use ($authRequest) {
            $response = $this->convertResponse(
                $this->server->completeAuthorizationRequest($authRequest, new Psr7Response()),
            );

            $this->storeAgent($response);

            return $response;
        });
    }
}
