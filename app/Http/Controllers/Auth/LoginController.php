<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Traits\LastLogInLocationTrait;
use App\Traits\Passport\PassportRedirectTrait;
use App\Traits\UserAlreadyLoginTrait;
use App\Traits\UserLogoutTrait;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;
    use PassportRedirectTrait;
    use UserLogoutTrait;
    use UserAlreadyLoginTrait;
    use LastLogInLocationTrait;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = '/home';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
        $this->middleware('auth')->only('logout');
    }

    /**
     * Show the application's login form.
     *
     * @return \Illuminate\View\View
     */
    public function showLoginForm()
    {
        $this->setRedirectUri();

        $params = $this->isRegister();
        if ($params !== false && !$this->isUserExist($params['email'] ?? '')) {
            if ($this->isGmail($params['email'] ?? '')) {
                return to_route('passport.socialite.redirect', 'google');
            }

            return to_route('register', $params);
        }

        $email = $params['email'] ?? '';

        return view('auth.login', compact('email'));
    }

    /**
     * Log the user out of the application.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\JsonResponse
     */
    public function logout(Request $request)
    {
        $userId = session()->get('verify_user_id');

        $this->guard()->logout();

        Session::put('verify_user_id', $userId);

        if ($response = $this->loggedOut($request)) {
            return $response;
        }

        return $request->wantsJson()
            ? new JsonResponse([], 204)
            : redirect('/');
    }

    /**
     * Force login a user.
     *
     * @param  \Illuminate\Http\Request  $request
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function forceLogin(Request $request)
    {
        $user = User::where('email', $request->email)->first();

        if (! $user) {
            return redirect()->route('login')->withErrors(['email' => 'User not found']);
        }

        Auth::login($user, true);
        $this->updateLastLogInLocation();

        if ($user->verified != 1) {
            return $request->wantsJson()
                ? new JsonResponse(['message' => 'Your email address is not verified.'], 409)
                : redirect()->route('verification.notice');
        }

        $this->revokeGWToken();

        // Check if we have an authorization request in the session
        if ($request->session()->has('authRequest')) {
            $authRequest = $request->session()->pull('authRequest');
            $filteredParams = array_diff_key($authRequest, array_flip(['is_register', 'email']));

            // Build the authorization URL
            $query = http_build_query($filteredParams);
            return redirect('/oauth/authorize?' . $query);
        }

        // Check if we have an authorization request in the session
        if (session()->has('redirect_uri')) {
            return redirect(Session::get('redirect_uri'));
        }

        return redirect()->route('login');
    }

    /**
     * Send the response after the user was authenticated.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\JsonResponse
     */
    protected function sendLoginResponse(Request $request)
    {
        $request->session()->regenerate();

        if ($this->isUserAlreadyLogin()) {
            $this->revokeAll();

            return to_route('login')->withErrors(['is_already_logged_in' => true])->with(['email' => $request->email]);
        }

        $this->updateLastLogInLocation();
        $this->clearLoginAttempts($request);

        createInitSubscription($this->guard()->user());

        // Check if the user has verified their email address
        $user = $this->guard()->user();
        if ($user->verified != 1) {
            return $request->wantsJson()
                ? new JsonResponse(['message' => 'Your email address is not verified.'], 409)
                : redirect()->route('verification.notice');
        }

        if ($response = $this->authenticated($request, $user)) {
            return $response;
        }

        // Check if we have an authorization request in the session
        if ($request->session()->has('authRequest')) {
            $authRequest = $request->session()->pull('authRequest');
            $filteredParams = array_diff_key($authRequest, array_flip(['is_register', 'email']));
            // Build the authorization URL
            $query = http_build_query($filteredParams);
            return redirect('/oauth/authorize?' . $query);
        }

        // Check if we have an authorization request in the session
        if (session()->has('redirect_uri')) {
            return redirect(Session::get('redirect_uri'));
        }

        return $request->wantsJson()
            ? new JsonResponse([], 204)
            : redirect()->intended($this->redirectPath());
    }

    /**
     * Attempt to log the user into the application.
     *
     * @param  \Illuminate\Http\Request  $request
     *
     * @return bool
     */
    protected function attemptLogin(Request $request)
    {
        return $this->guard()->attempt($this->credentials($request), true);
    }
}
